[project]
name = "rls-tut"
version = "0.1.0"
description = "Project to test and learn RLS in postgres"
readme = "README.md"
authors = [
    { name = "<PERSON><PERSON><PERSON><PERSON> <PERSON>", email = "<EMAIL>" }
]
requires-python = ">=3.12"
dependencies = [
    "fastapi>=0.116.1",
    "passlib[bcrypt]>=1.7.4",
    "psycopg[binary,pool]>=3.2.9",
    "pydantic-settings>=2.10.1",
    "python-jose>=3.5.0",
    "sqlmodel>=0.0.24",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[dependency-groups]
dev = [
    "black>=25.1.0",
    "ipython>=9.4.0",
    "isort>=6.0.1",
    "mypy>=1.17.1",
    "ruff>=0.12.8",
    "ty>=0.0.1a17",
]
