import uuid
from datetime import datetime

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, DateTime, String
from sqlalchemy.dialects.postgresql import UUID

from rls_tut.core.utils import utc_now

from . import Base


class User(Base):
    __tablename__ = "users"
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email = Column(String, unique=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    is_admin = Column(Boolean, default=False)
    created_at = Column(DateTime, default=utc_now)
    updated_at = Column(DateTime, default=utc_now, onupdate=utc_now)
