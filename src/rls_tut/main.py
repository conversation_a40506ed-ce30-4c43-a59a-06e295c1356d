from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException
from sqlalchemy.orm import Session
from passlib.context import <PERSON><PERSON><PERSON><PERSON>xt
from pydantic import BaseModel, EmailStr

from rls_tut.database.models import User
from rls_tut.core.dependencies import get_db

app = FastAPI()
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


# --- Pydantic model for input ---
class RegisterRequest(BaseModel):
    email: EmailStr
    password: str


# --- Registration handler ---
@app.post("/register")
def register(user: RegisterRequest, db: Session = Depends(get_db)):
    # Validate email uniqueness
    existing = db.query(User).filter(User.email == user.email).first()
    if existing:
        raise HTTPException(status_code=400, detail="Email already registered")
    # Hash password
    hashed_pw = pwd_context.hash(user.password)
    # Create user
    new_user = User(email=user.email, hashed_password=hashed_pw)
    db.add(new_user)
    db.commit()
    db.refresh(new_user)
    return {"id": str(new_user.id), "email": new_user.email}
