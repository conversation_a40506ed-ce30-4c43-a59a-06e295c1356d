FROM python:3.12-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Install uv for fast Python package management
RUN pip install uv

# Copy dependency files
COPY README.md pyproject.toml uv.lock ./

# Install Python dependencies
RUN uv sync --frozen

# Copy source code (this will be overridden by the volume mount in development)
COPY .env .
COPY src/ ./src/

# Expose port
EXPOSE 8000

# Default command (can be overridden in docker-compose.yml)
CMD ["uvicorn", "rls_tut.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
